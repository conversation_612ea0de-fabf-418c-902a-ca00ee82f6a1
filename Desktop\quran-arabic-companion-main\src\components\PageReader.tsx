import React from 'react';
import QuranPageViewer from './QuranPageViewer';

interface PageReaderProps {
  initialPage?: number;
  onBack?: () => void;
}

const PageReader: React.FC<PageReaderProps> = ({ initialPage = 1, onBack }) => {
  const [currentPage, setCurrentPage] = React.useState(initialPage);

  return (
    <QuranPageViewer
      pageNumber={currentPage}
      onBack={onBack || (() => {})}
      onPageChange={setCurrentPage}
      totalPages={604}
    />
  );
};

export default PageReader;
