import React, { useState, useEffect } from 'react';
import { useSwipeable } from 'react-swipeable';
import { Moon, Sun, Home } from 'lucide-react';
import surahsInfo from '../data/surah.json';
import ayahData from '../data/ayahobject.json';

interface Ayah {
  number: number;
  uthmaniText: string;
  text: string;
  surah: {
    number: number;
    name: string;
    englishName: string;
    englishNameTranslation: string;
    numberOfAyahs: number;
    revelationType: string;
  };
  numberInSurah: number;
  numberInSurahNative: string;
  juz: number;
  manzil: number;
  page: number;
  ruku: number;
  hizbQuarter: number;
  sajda: boolean;
}

interface QuranPageViewerProps {
  pageNumber: number;
  onBack: () => void;
  onPageChange: (newPage: number) => void;
  totalPages?: number;
  surahName?: string;
  juzNumber?: number;
}

const QuranPageViewer: React.FC<QuranPageViewerProps> = ({
  pageNumber,
  onBack,
  onPageChange,
  totalPages = 604,
  surahName,
  juzNumber
}) => {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [pageData, setPageData] = useState<any>(null);
  const [pageAyahs, setPageAyahs] = useState<Ayah[]>([]);

  // Load dark mode preference
  useEffect(() => {
    const savedDarkMode = localStorage.getItem('quran-dark-mode');
    setIsDarkMode(savedDarkMode === 'dark');
  }, []);

  // Save dark mode preference
  const toggleDarkMode = () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    localStorage.setItem('quran-dark-mode', newMode ? 'dark' : 'light');
  };

  // Load page ayahs from ayahobject.json
  useEffect(() => {
    const loadPageAyahs = () => {
      try {
        const ayahs = (ayahData as Ayah[]).filter((ayah) => ayah.page === pageNumber);
        setPageAyahs(ayahs);

        // Set page data based on first ayah
        if (ayahs.length > 0) {
          const firstAyah = ayahs[0];
          setPageData({
            page: pageNumber,
            juz: firstAyah.juz,
            surahs: [{
              surah: firstAyah.surah.number,
              name: firstAyah.surah.name,
              verses: ayahs.filter(a => a.surah.number === firstAyah.surah.number).map(a => a.numberInSurah)
            }]
          });
        }
      } catch (error) {
        console.error("Failed to load page ayahs", error);
      }
    };
    loadPageAyahs();
  }, [pageNumber]);

  // Swipe handlers
  const handlers = useSwipeable({
    onSwipedLeft: () => pageNumber < totalPages && onPageChange(pageNumber + 1),
    onSwipedRight: () => pageNumber > 1 && onPageChange(pageNumber - 1),
    trackMouse: true,
  });

  // Get surah name from page data
  const getSurahName = () => {
    if (!pageData || !pageData.surahs || pageData.surahs.length === 0) return '';
    const firstSurah = pageData.surahs[0];
    const surahInfo = surahsInfo.find(s => parseInt(s.index) === firstSurah.surah);
    return surahInfo ? `سورة ${surahInfo.titleAr}` : '';
  };

  // Get juz name
  const getJuzName = () => {
    if (!pageData) return '';
    return pageData.juz ? `الجزء ${convertToArabicNumbers(pageData.juz)}` : '';
  };

  // Convert numbers to Arabic
  const convertToArabicNumbers = (num: number) => {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return num.toString().split('').map(digit =>
      /\d/.test(digit) ? arabicNumbers[parseInt(digit)] : digit
    ).join('');
  };

  // Component for verse number circle - Traditional Islamic style
  const VerseNumber: React.FC<{ number: number }> = ({ number }) => (
    <span className={`relative inline-flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold mx-2 ${
      isDarkMode
        ? 'bg-gradient-to-br from-amber-500 to-amber-700 text-white border-2 border-amber-400 shadow-lg'
        : 'bg-gradient-to-br from-amber-200 to-amber-400 text-amber-900 border-2 border-amber-500 shadow-md'
    }`} style={{
      fontFamily: 'Amiri, serif',
      boxShadow: isDarkMode
        ? '0 4px 12px rgba(245, 158, 11, 0.4), inset 0 1px 2px rgba(255, 255, 255, 0.2)'
        : '0 4px 12px rgba(245, 158, 11, 0.3), inset 0 1px 2px rgba(255, 255, 255, 0.5)'
    }}>
      {convertToArabicNumbers(number)}
      {/* Decorative inner circle */}
      <span className={`absolute inset-1 rounded-full border ${
        isDarkMode ? 'border-amber-300/30' : 'border-amber-600/30'
      }`} />
    </span>
  );

  // Component for surah header - Traditional Islamic ornate style
  const SurahHeader: React.FC<{ surahName: string; surahNumber: number }> = ({ surahName, surahNumber }) => (
    <div className="relative mb-8">
      {/* Main ornate frame */}
      <div className={`relative text-center py-6 px-8 mx-4 rounded-2xl ${
        isDarkMode
          ? 'bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900 border-3 border-amber-500'
          : 'bg-gradient-to-br from-blue-50 via-white to-blue-50 border-3 border-amber-400'
      }`} style={{
        boxShadow: isDarkMode
          ? '0 8px 32px rgba(245, 158, 11, 0.3), inset 0 2px 4px rgba(255, 255, 255, 0.1)'
          : '0 8px 32px rgba(245, 158, 11, 0.2), inset 0 2px 4px rgba(255, 255, 255, 0.8)'
      }}>

        {/* Decorative corners */}
        <div className={`absolute top-2 left-2 w-4 h-4 border-t-2 border-l-2 ${
          isDarkMode ? 'border-amber-400' : 'border-amber-500'
        } rounded-tl-lg`} />
        <div className={`absolute top-2 right-2 w-4 h-4 border-t-2 border-r-2 ${
          isDarkMode ? 'border-amber-400' : 'border-amber-500'
        } rounded-tr-lg`} />
        <div className={`absolute bottom-2 left-2 w-4 h-4 border-b-2 border-l-2 ${
          isDarkMode ? 'border-amber-400' : 'border-amber-500'
        } rounded-bl-lg`} />
        <div className={`absolute bottom-2 right-2 w-4 h-4 border-b-2 border-r-2 ${
          isDarkMode ? 'border-amber-400' : 'border-amber-500'
        } rounded-br-lg`} />

        {/* Inner decorative border */}
        <div className={`absolute inset-4 border rounded-xl ${
          isDarkMode ? 'border-amber-400/30' : 'border-amber-500/30'
        }`} />

        {/* Surah name with traditional styling */}
        <div className={`text-3xl font-bold mb-2 ${
          isDarkMode ? 'text-amber-200' : 'text-blue-900'
        }`} style={{
          fontFamily: 'Amiri, serif',
          textShadow: isDarkMode
            ? '0 2px 4px rgba(0, 0, 0, 0.5)'
            : '0 1px 2px rgba(0, 0, 0, 0.1)'
        }}>
          {surahName}
        </div>

        {/* Bismillah for non-Fatiha surahs */}
        {surahNumber > 1 && (
          <div className={`text-xl mt-4 ${
            isDarkMode ? 'text-gray-200' : 'text-gray-700'
          }`} style={{ fontFamily: 'Amiri, serif', lineHeight: '1.8' }}>
            بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ
          </div>
        )}
      </div>
    </div>
  );

  // Component for page content
  const PageContent: React.FC = () => {
    if (pageAyahs.length === 0) return null;

    const groupedBySurah = pageAyahs.reduce((acc, ayah) => {
      const surahNumber = ayah.surah.number;
      if (!acc[surahNumber]) {
        acc[surahNumber] = [];
      }
      acc[surahNumber].push(ayah);
      return acc;
    }, {} as Record<number, Ayah[]>);

    return (
      <div className="space-y-8">
        {Object.entries(groupedBySurah).map(([surahNumber, ayahs]) => {
          const firstAyah = ayahs[0];
          const isNewSurah = firstAyah.numberInSurah === 1;

          return (
            <div key={surahNumber} className="space-y-6">
              {isNewSurah && (
                <SurahHeader
                  surahName={firstAyah.surah.name}
                  surahNumber={parseInt(surahNumber)}
                />
              )}

              <div className={`text-right leading-loose text-3xl ${
                isDarkMode ? 'text-gray-100' : 'text-gray-800'
              }`} style={{
                fontFamily: 'Scheherazade New, Amiri, serif',
                lineHeight: '3.2',
                textAlign: 'justify',
                textJustify: 'inter-word',
                fontWeight: '500',
                letterSpacing: '0.02em',
                wordSpacing: '0.1em',
                fontFeatureSettings: '"liga" 1, "dlig" 1, "kern" 1, "mark" 1, "mkmk" 1',
                textRendering: 'optimizeLegibility'
              }}>
                {ayahs.map((ayah, index) => (
                  <span key={ayah.number} className="inline-block">
                    <span className={`hover:rounded px-1 py-0.5 transition-all duration-200 ${
                      isDarkMode
                        ? 'hover:bg-amber-600/20'
                        : 'hover:bg-amber-100/30'
                    }`}>
                      {ayah.uthmaniText}
                    </span>
                    <VerseNumber number={ayah.numberInSurah} />
                    {/* Sajda marker with enhanced styling */}
                    {ayah.sajda && (
                      <span className={`inline-flex items-center justify-center mx-2 w-8 h-8 rounded-full text-xl ${
                        isDarkMode
                          ? 'bg-green-600 text-white border-2 border-green-400'
                          : 'bg-green-100 text-green-700 border-2 border-green-500'
                      }`} title="سجدة" style={{
                        boxShadow: isDarkMode
                          ? '0 4px 12px rgba(34, 197, 94, 0.4)'
                          : '0 4px 12px rgba(34, 197, 94, 0.3)'
                      }}>
                        ۩
                      </span>
                    )}
                    {/* Hizb Quarter marker with enhanced styling */}
                    {ayah.hizbQuarter && ayah.hizbQuarter % 4 === 1 && ayah.numberInSurah > 1 && (
                      <span className={`inline-flex items-center justify-center mx-2 w-10 h-10 rounded-full text-2xl ${
                        isDarkMode
                          ? 'bg-blue-600 text-white border-2 border-blue-400'
                          : 'bg-blue-100 text-blue-700 border-2 border-blue-500'
                      }`} title="ربع الحزب" style={{
                        boxShadow: isDarkMode
                          ? '0 4px 12px rgba(59, 130, 246, 0.4)'
                          : '0 4px 12px rgba(59, 130, 246, 0.3)'
                      }}>
                        ۞
                      </span>
                    )}
                    {/* Add space between verses */}
                    {index < ayahs.length - 1 && <span className="mx-1"> </span>}
                  </span>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const backgroundClass = isDarkMode
    ? 'bg-gradient-to-br from-gray-900 via-slate-900 to-gray-800'
    : 'bg-gradient-to-br from-[#faf8f3] via-[#f7f5f0] to-[#f5f2e8]';

  const textClass = isDarkMode ? 'text-white' : 'text-gray-800';
  const headerTextClass = isDarkMode ? 'text-gray-300' : 'text-amber-700';

  return (
    <div className={`min-h-screen ${backgroundClass} relative select-none`}>
      {/* Header with surah and juz info */}
      <div className="absolute top-0 left-0 w-full flex justify-between items-center px-6 py-4 z-10">
        {/* Juz number in corner - Traditional style */}
        {pageData && pageData.juz && (
          <div className={`${
            isDarkMode
              ? 'bg-gradient-to-br from-amber-600 to-amber-800 border-amber-400'
              : 'bg-gradient-to-br from-amber-200 to-amber-400 border-amber-600'
          } border-2 rounded-full w-12 h-12 flex items-center justify-center shadow-lg`}>
            <span className={`text-sm font-bold ${
              isDarkMode ? 'text-white' : 'text-amber-900'
            }`} style={{ fontFamily: 'Amiri, serif' }}>
              {convertToArabicNumbers(pageData.juz)}
            </span>
          </div>
        )}

        <span className={`text-lg font-bold ${headerTextClass}`} style={{ fontFamily: 'Amiri, serif' }}>
          {getSurahName()}
        </span>
      </div>

      {/* Top right controls */}
      <div className="fixed top-4 right-4 flex gap-2 z-20">
        {/* Dark mode toggle */}
        <button
          onClick={toggleDarkMode}
          className={`p-2 rounded-full shadow-lg transition-all border ${
            isDarkMode
              ? 'bg-gray-700/80 hover:bg-gray-700/100 text-yellow-400 border-gray-600'
              : 'bg-white/80 hover:bg-white/100 text-gray-800 border-gray-200'
          }`}
          style={{ width: 44, height: 44, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
          aria-label={isDarkMode ? 'الوضع النهاري' : 'الوضع الليلي'}
        >
          {isDarkMode ? <Sun size={20} /> : <Moon size={20} />}
        </button>

        {/* Home button */}
        <button
          onClick={onBack}
          className={`p-2 rounded-full shadow-lg transition-all border ${
            isDarkMode
              ? 'bg-gray-700/80 hover:bg-gray-700/100 text-white border-gray-600'
              : 'bg-white/80 hover:bg-white/100 text-gray-800 border-gray-200'
          }`}
          style={{ width: 44, height: 44, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
          aria-label="الصفحة الرئيسية"
        >
          <Home size={20} />
        </button>
      </div>

      {/* Main page content */}
      <div className="pt-16 pb-20 px-4" {...handlers}>
        <div className={`max-w-4xl mx-auto relative ${
          isDarkMode
            ? 'bg-gradient-to-br from-gray-800/90 via-gray-800/80 to-gray-900/90 border-amber-500/50'
            : 'bg-gradient-to-br from-amber-50/90 via-white/95 to-amber-50/90 border-amber-300'
        } border-3 rounded-3xl shadow-2xl p-8 min-h-[80vh]`} style={{
          boxShadow: isDarkMode
            ? '0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(245, 158, 11, 0.2)'
            : '0 25px 50px -12px rgba(245, 158, 11, 0.2), 0 0 0 1px rgba(245, 158, 11, 0.1)'
        }}>

          {/* Traditional Islamic pattern overlay */}
          <div className="absolute inset-0 opacity-5 rounded-3xl" style={{
            backgroundImage: `radial-gradient(circle at 25% 25%, rgba(245, 158, 11, 0.1) 0%, transparent 25%),
                             radial-gradient(circle at 75% 75%, rgba(245, 158, 11, 0.1) 0%, transparent 25%)`,
            backgroundSize: '40px 40px'
          }} />

          {/* Inner decorative border */}
          <div className={`relative border-2 ${
            isDarkMode ? 'border-amber-500/30' : 'border-amber-400/40'
          } rounded-2xl p-6 h-full`}>

            {/* Corner decorations */}
            <div className={`absolute top-2 left-2 w-6 h-6 border-t-2 border-l-2 ${
              isDarkMode ? 'border-amber-400/50' : 'border-amber-500/60'
            } rounded-tl-xl`} />
            <div className={`absolute top-2 right-2 w-6 h-6 border-t-2 border-r-2 ${
              isDarkMode ? 'border-amber-400/50' : 'border-amber-500/60'
            } rounded-tr-xl`} />
            <div className={`absolute bottom-2 left-2 w-6 h-6 border-b-2 border-l-2 ${
              isDarkMode ? 'border-amber-400/50' : 'border-amber-500/60'
            } rounded-bl-xl`} />
            <div className={`absolute bottom-2 right-2 w-6 h-6 border-b-2 border-r-2 ${
              isDarkMode ? 'border-amber-400/50' : 'border-amber-500/60'
            } rounded-br-xl`} />

            <PageContent />

          </div>
        </div>
      </div>

      {/* Page number at bottom - Traditional ornate style */}
      <div className="absolute bottom-6 left-0 w-full flex justify-center items-center z-10 pointer-events-none">
        <div className={`relative inline-flex items-center justify-center px-8 py-4 rounded-2xl shadow-2xl ${
          isDarkMode
            ? 'bg-gradient-to-br from-amber-700 via-amber-600 to-amber-800 border-3 border-amber-400'
            : 'bg-gradient-to-br from-amber-200 via-amber-100 to-amber-300 border-3 border-amber-500'
        }`} style={{
          boxShadow: isDarkMode
            ? '0 8px 32px rgba(245, 158, 11, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.2)'
            : '0 8px 32px rgba(245, 158, 11, 0.3), inset 0 2px 4px rgba(255, 255, 255, 0.6)'
        }}>
          {/* Decorative corners */}
          <div className={`absolute top-1 left-1 w-3 h-3 border-t-2 border-l-2 ${
            isDarkMode ? 'border-amber-300' : 'border-amber-700'
          } rounded-tl-lg`} />
          <div className={`absolute top-1 right-1 w-3 h-3 border-t-2 border-r-2 ${
            isDarkMode ? 'border-amber-300' : 'border-amber-700'
          } rounded-tr-lg`} />
          <div className={`absolute bottom-1 left-1 w-3 h-3 border-b-2 border-l-2 ${
            isDarkMode ? 'border-amber-300' : 'border-amber-700'
          } rounded-bl-lg`} />
          <div className={`absolute bottom-1 right-1 w-3 h-3 border-b-2 border-r-2 ${
            isDarkMode ? 'border-amber-300' : 'border-amber-700'
          } rounded-br-lg`} />

          <span className={`text-3xl font-bold ${
            isDarkMode ? 'text-white' : 'text-amber-900'
          }`} style={{
            fontFamily: 'Amiri, serif',
            textShadow: isDarkMode
              ? '0 2px 4px rgba(0, 0, 0, 0.5)'
              : '0 1px 2px rgba(0, 0, 0, 0.2)'
          }}>
            {convertToArabicNumbers(pageNumber)}
          </span>
        </div>
      </div>
    </div>
  );
};

export default QuranPageViewer;